<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 服务器认证配置 -->
    <servers>
        <server>
            <id>maven-releases</id>
            <username>admin</username>
            <password>yunzheng2025.</password>
        </server>
        <server>
            <id>maven-snapshots</id>
            <username>admin</username>
            <password>yunzheng2025.</password>
        </server>
    </servers>

    <!-- 镜像配置 - 优化仓库访问顺序 -->
    <mirrors>
        <!-- 阿里云镜像 - 镜像所有外部仓库，提供更快的下载速度 -->
        <mirror>
            <id>aliyun-maven</id>
            <name><PERSON>yun Maven Mirror</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <mirrorOf>external:*,!maven-releases,!maven-snapshots</mirrorOf>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <!-- 私服配置 - 优化仓库顺序和更新策略 -->
        <profile>
            <id>yunzheng-nexus</id>
            <repositories>
                <!-- 私服公共仓库 - 包含第三方依赖 -->
                <repository>
                    <id>maven-public</id>
                    <name>Maven Public Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy> <!-- 每次都检查最新版本 -->
                    </snapshots>
                </repository>

                <!-- 私服SNAPSHOT仓库 - 仅用于内部SNAPSHOT依赖 -->
                <repository>
                    <id>maven-snapshots</id>
                    <name>Maven Snapshots Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-snapshots/</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy> <!-- 每次都检查最新版本 -->
                    </snapshots>
                </repository>

                <!-- 私服Release仓库 -->
                <repository>
                    <id>maven-releases</id>
                    <name>Maven Releases Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>

            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <name>Maven Public Repository</name>
                    <url>https://maven.yunzheng.work/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- JDK 版本配置 -->
        <profile>
            <id>jdk-21</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>21</jdk>
            </activation>
            <properties>
                <maven.compiler.source>21</maven.compiler.source>
                <maven.compiler.target>21</maven.compiler.target>
                <maven.compiler.compilerVersion>21</maven.compiler.compilerVersion>
            </properties>
        </profile>
    </profiles>

    <!-- 激活的配置文件 -->
    <activeProfiles>
        <activeProfile>yunzheng-nexus</activeProfile>
        <activeProfile>jdk-21</activeProfile>
    </activeProfiles>

</settings>
