server:
  port: 9528
  servlet:
    context-path: /rd-platform

spring:
  application:
    name: rd-platform
  config:
    import: optional:classpath:application-generated.yml
  datasource:
    url: ${DATABASE_URL:****************************************}
    username: ${POLARDB_USER:yunzheng}
    password: ${POLARDB_PASSWORD:yunzheng}
    driver-class-name: org.postgresql.Driver
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  builtin:
    super:
      admin:
        # 是否启用内置超管账号
        enabled: true
        # 超管用户名（建议通过环境变量设置）
        username: ${BUILTIN_SUPER_ADMIN_USERNAME:super@Admin}
        # 超管密码（建议通过环境变量设置）
        password: ${BUILTIN_SUPER_ADMIN_PASSWORD:af81527ff39d63b7b78dddfaa2e6b545}
logging:
  config: classpath:logback-prod.xml

hasura:
  server:
    url: http://hasura:8080
    admin:
      secret: 1234

# 外部配置服务配置
config:
  service:
    # 配置服务主机地址（生产环境通过环境变量CONFIG_SERVICE_HOST设置）
    host: ${CONFIG_SERVICE_HOST:************}
    # 配置服务端口（生产环境通过环境变量CONFIG_SERVICE_PORT设置）
    port: ${CONFIG_SERVICE_PORT:5000}

login:
  ignoreUrl: /api/health/check

# xxl-job配置
xxl:
  job:
    admin:
      # xxl-job调度中心地址（生产环境通过容器内部网络访问）
      addresses: http://xxl-job-admin:8898/xxl-job-admin
    executor:
      # 执行器名称
      appname: rd-scaffold-executor
      # 执行器IP，为空则自动获取
      ip: 
      # 执行器端口
      port: 9999
      # 执行器日志路径
      logpath: /tmp/xxl-job/jobhandler
      # 执行器日志保留天数
      logretentiondays: 30
    # 访问令牌（需要与调度中心保持一致）
    accessToken: default_token


# OpenAPI 模块配置文件

# OpenAPI 白名单配置
openapi:
  whitelist:
    # 白名单URL列表，所有URL必须以 /openUrl 开头
    urls:
      - /openUrl/health
      - /openUrl/status
      - /openUrl/ping
      - /openUrl/version

