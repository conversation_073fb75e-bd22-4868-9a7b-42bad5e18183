server:
  port: 9528
  servlet:
    context-path: /rd-platform
spring:
  application:
    name: rd-platform
  datasource:
    url: *********************************************
    username: yunzheng
    password: yunzheng
    driver-class-name: org.postgresql.Driver
    hikari:
      schema: public
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  builtin:
    super:
      admin:
        # 是否启用内置超管账号
        enabled: true
        # 超管用户名（建议通过环境变量设置）
        username: ${BUILTIN_SUPER_ADMIN_USERNAME:super@Admin}
        # 超管密码（建议通过环境变量设置）
        password: ${BUILTIN_SUPER_ADMIN_PASSWORD:af81527ff39d63b7b78dddfaa2e6b545}
# 权限配置
permission:
  # 开发环境默认给所有登录用户全部菜单权限
  default:
    all-menu-access: true
    # 是否跳过权限检查
    skip-permission-check: true

hasura:
  server:
    url: http://*************:8085
  endpoint: http://*************:8085
  admin:
    secret: 1234

# 外部配置服务配置
config:
  service:
    # 配置服务主机地址（可通过环境变量CONFIG_SERVICE_HOST覆盖）
    host: ${CONFIG_SERVICE_HOST:************}
    # 配置服务端口（可通过环境变量CONFIG_SERVICE_PORT覆盖）
    port: ${CONFIG_SERVICE_PORT:5000}
    # 图片识别服务配置
    extract:
      retry:
        # 重试次数（除了第一次请求）
        count: 2
        # 重试间隔时间（毫秒）
        delay: 1000


# OpenAPI 模块配置文件

# OpenAPI 白名单配置
openapi:
  whitelist:
    # 白名单URL列表，所有URL必须以 /openUrl 开头
    urls:
      - /openUrl/webhook/formbricks/response

