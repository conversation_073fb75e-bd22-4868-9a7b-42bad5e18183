package com.yunzheng.rd.scaffold.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotNull;

/**
 * 患者信息删除DTO
 * 用于删除患者信息的参数传递
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizPatientInfoDeleteDTO {

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long id;
}