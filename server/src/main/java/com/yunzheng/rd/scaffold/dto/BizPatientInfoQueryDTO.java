package com.yunzheng.rd.scaffold.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 患者信息查询DTO
 * 用于分页查询患者信息的参数传递
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizPatientInfoQueryDTO {

    /**
     * 病历号（支持模糊查询）
     */
    private String medicalRecordNumber;

    /**
     * 患者姓名（支持模糊查询）
     */
    private String patientName;

    /**
     * 页码（从1开始）
     */
    @Builder.Default
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Builder.Default
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @Builder.Default
    private String sortField = "createdAt";

    /**
     * 排序方向（ASC/DESC）
     */
    @Builder.Default
    private String sortDirection = "DESC";
}