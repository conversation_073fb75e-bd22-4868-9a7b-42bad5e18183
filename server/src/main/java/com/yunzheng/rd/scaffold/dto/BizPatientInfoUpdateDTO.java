package com.yunzheng.rd.scaffold.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者信息更新DTO
 * 用于接收患者信息更新请求的数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizPatientInfoUpdateDTO {

    /**
     * 主键ID（必填）
     */
    @NotNull(message = "患者ID不能为空")
    private Long id;

    /**
     * 病历号
     */
    @Size(max = 50, message = "病历号长度不能超过50个字符")
    private String medicalRecordNumber;

    /**
     * 病人姓名
     */
    @Size(max = 100, message = "患者姓名长度不能超过100个字符")
    private String patientName;

    /**
     * 性别：0未知 1男 2女
     */
    private Integer gender;

    /**
     * 疾病类型
     */
    @Size(max = 100, message = "疾病类型长度不能超过100个字符")
    private String diseaseType;

    /**
     * 入院时间
     */

    private LocalDateTime admissionTime;

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 手术史
     */
    private String surgicalHistory;

    /**
     * 既往用药史
     */
    private String pastMedicationHistory;

    /**
     * 居住地址
     */
    @Size(max = 200, message = "居住地址长度不能超过200个字符")
    private String residence;

    /**
     * 联系信息
     */
    @Size(max = 200, message = "联系信息长度不能超过200个字符")
    private String contactInfo;

    /**
     * 电话号码
     */
    @Size(max = 20, message = "电话号码长度不能超过20个字符")
    private String phone;

    /**
     * 出生日期
     */
    private LocalDate dateOfBirth;

    /**
     * 主要诊断
     */
    @Size(max = 200, message = "主要诊断长度不能超过200个字符")
    private String mainDiagnosis;

    /**
     * 紧急联系人姓名
     */
    @Size(max = 100, message = "紧急联系人姓名长度不能超过100个字符")
    private String emergencyContactName;

    /**
     * 紧急联系人电话
     */
    @Size(max = 200, message = "紧急联系人电话长度不能超过20个字符")
    private String emergencyContactPhone;

    /**
     * 病例原始文本
     */
    private String caseRawText;
}