package com.yunzheng.rd.scaffold.service;

import com.yunzheng.capability.commons.model.vo.PageVO;
import com.yunzheng.capability.commons.model.vo.ResponseVO;
import com.yunzheng.rd.scaffold.domain.BizPatientInfo;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoDeleteDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoListDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoQueryDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoUpdateDTO;
import com.yunzheng.rd.scaffold.repository.BizPatientInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 患者信息服务类患者信息服务类
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class BizPatientInfoService {

    @Autowired
    private BizPatientInfoRepository patientInfoRepository;

    /**
     * 根据ID查询患者信息
     * 
     * @param id 患者ID
     * @return 患者信息
     */
    public ResponseVO<BizPatientInfoDTO> getById(Long id) {
        try {
            if (id == null) {
                return ResponseVO.error("患者ID不能为空");
            }

            Optional<BizPatientInfo> patientOpt = patientInfoRepository.findById(id);
            if (patientOpt.isEmpty()) {
                return ResponseVO.error("患者信息不存在");
            }

            BizPatientInfoDTO dto = convertToDTO(patientOpt.get());
            return ResponseVO.ok(dto);
        } catch (Exception e) {
            log.error("查询患者信息失败，ID: {}", id, e);
            return ResponseVO.error("查询患者信息失败");
        }
    }

    /**
     * 分页查询患者信息列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public ResponseVO<PageVO<BizPatientInfoListDTO>> page(BizPatientInfoQueryDTO queryDTO) {
        try {
            // 参数校验和默认值处理
            if (queryDTO == null) {
                queryDTO = new BizPatientInfoQueryDTO();
            }

            // 确保分页参数有效
            Integer pageNum = queryDTO.getPageNum() != null ? queryDTO.getPageNum() : 1;
            Integer pageSize = queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10;

            // 确保排序参数有效
            String sortField = queryDTO.getSortField() != null ? queryDTO.getSortField() : "createdAt";
            String sortDirection = queryDTO.getSortDirection() != null ? queryDTO.getSortDirection() : "DESC";

            // 参数范围校验
            pageNum = Math.max(1, pageNum);
            pageSize = Math.min(100, Math.max(1, pageSize));

            // 构建分页参数
            Sort sort = buildSort(sortField, sortDirection);
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize, sort);

            // 构建动态查询条件
            Specification<BizPatientInfo> specification = buildSpecification(queryDTO);

            // 执行分页查询
            Page<BizPatientInfo> page = patientInfoRepository.findAll(specification, pageable);

            // 转换为ListDTO（不包含大字段）
            List<BizPatientInfoListDTO> records = page.getContent().stream()
                    .map(this::convertToListDTO)
                    .collect(Collectors.toList());

            // 构建分页响应
            PageVO<BizPatientInfoListDTO> pageVO = new PageVO<>(page.getTotalElements(), records);
            return ResponseVO.ok(pageVO);
        } catch (Exception e) {
            log.error("分页查询患者信息失败", e);
            return ResponseVO.error("查询患者信息列表失败");
        }
    }

    /**
     * 更新患者信息
     * 
     * @param updateDTO 更新参数
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<BizPatientInfoDTO> update(BizPatientInfoUpdateDTO updateDTO) {
        try {
            if (updateDTO == null || updateDTO.getId() == null) {
                return ResponseVO.error("患者ID不能为空");
            }

            Optional<BizPatientInfo> optionalEntity = patientInfoRepository.findById(updateDTO.getId());
            if (optionalEntity.isEmpty()) {
                return ResponseVO.error("患者信息不存在");
            }

            BizPatientInfo entity = optionalEntity.get();
            
            // 更新字段（只更新非空字段）
            if (updateDTO.getMedicalRecordNumber() != null) {
                entity.setMedicalRecordNumber(updateDTO.getMedicalRecordNumber());
            }
            if (updateDTO.getPatientName() != null) {
                entity.setPatientName(updateDTO.getPatientName());
            }
            if (updateDTO.getGender() != null) {
                entity.setGender(updateDTO.getGender());
            }
            if (updateDTO.getDiseaseType() != null) {
                entity.setDiseaseType(updateDTO.getDiseaseType());
            }
            if (updateDTO.getAdmissionTime() != null) {
                entity.setAdmissionTime(updateDTO.getAdmissionTime());
            }
            if (updateDTO.getScore() != null) {
                entity.setScore(updateDTO.getScore());
            }
            if (updateDTO.getPastHistory() != null) {
                entity.setPastHistory(updateDTO.getPastHistory());
            }
            if (updateDTO.getSurgicalHistory() != null) {
                entity.setSurgicalHistory(updateDTO.getSurgicalHistory());
            }
            if (updateDTO.getPastMedicationHistory() != null) {
                entity.setPastMedicationHistory(updateDTO.getPastMedicationHistory());
            }
            if (updateDTO.getResidence() != null) {
                entity.setResidence(updateDTO.getResidence());
            }
            if (updateDTO.getContactInfo() != null) {
                entity.setContactInfo(updateDTO.getContactInfo());
            }
            if (updateDTO.getPhone() != null) {
                entity.setPhone(updateDTO.getPhone());
            }
            if (updateDTO.getDateOfBirth() != null) {
                entity.setDateOfBirth(updateDTO.getDateOfBirth());
            }
            if (updateDTO.getMainDiagnosis() != null) {
                entity.setMainDiagnosis(updateDTO.getMainDiagnosis());
            }
            if (updateDTO.getEmergencyContactName() != null) {
                entity.setEmergencyContactName(updateDTO.getEmergencyContactName());
            }
            if (updateDTO.getEmergencyContactPhone() != null) {
                entity.setEmergencyContactPhone(updateDTO.getEmergencyContactPhone());
            }
            if (updateDTO.getCaseRawText() != null) {
                entity.setCaseRawText(updateDTO.getCaseRawText());
            }

            BizPatientInfo savedEntity = patientInfoRepository.save(entity);
            BizPatientInfoDTO result = convertToDTO(savedEntity);
            
            log.info("患者信息更新成功，ID: {}", updateDTO.getId());
            return ResponseVO.ok(result);
        } catch (Exception e) {
            log.error("更新患者信息失败，ID: {}", updateDTO.getId(), e);
            return ResponseVO.error("更新患者信息失败");
        }
    }

    /**
     * 删除患者信息
     * 
     * @param deleteDTO 删除参数
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseVO<Boolean> delete(BizPatientInfoDeleteDTO deleteDTO) {
        try {
            if (deleteDTO == null || deleteDTO.getId() == null) {
                return ResponseVO.error("患者ID不能为空");
            }

            // 检查患者是否存在
            if (!patientInfoRepository.existsById(deleteDTO.getId())) {
                return ResponseVO.error("患者信息不存在");
            }

            // 执行删除
            patientInfoRepository.deleteById(deleteDTO.getId());
            log.info("删除患者信息成功，ID: {}", deleteDTO.getId());
            return ResponseVO.ok(true, "删除成功");
        } catch (Exception e) {
            log.error("删除患者信息失败，ID: {}", deleteDTO.getId(), e);
            return ResponseVO.error("删除患者信息失败");
        }
    }

    /**
     * 构建排序条件
     * 
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @return Sort对象
     */
    private Sort buildSort(String sortField, String sortDirection) {
        Sort.Direction direction = "ASC".equalsIgnoreCase(sortDirection) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
        
        // 校验排序字段，避免SQL注入
        switch (sortField) {
            case "id":
            case "medicalRecordNumber":
            case "patientName":
            case "admissionTime":
            case "createdAt":
            case "updatedAt":
                return Sort.by(direction, sortField);
            default:
                return Sort.by(Sort.Direction.DESC, "createdAt");
        }
    }

    /**
     * 构建动态查询条件
     * 
     * @param queryDTO 查询条件
     * @return Specification查询条件
     */
    private Specification<BizPatientInfo> buildSpecification(BizPatientInfoQueryDTO queryDTO) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 病历号模糊查询
            if (StringUtils.hasText(queryDTO.getMedicalRecordNumber())) {
                predicates.add(criteriaBuilder.like(root.get("medicalRecordNumber"), 
                        "%" + queryDTO.getMedicalRecordNumber() + "%"));
            }

            // 患者姓名模糊查询
            if (StringUtils.hasText(queryDTO.getPatientName())) {
                predicates.add(criteriaBuilder.like(root.get("patientName"), 
                        "%" + queryDTO.getPatientName() + "%"));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 转换实体为详情DTO（包含所有字段）
     * 
     * @param entity 实体对象
     * @return DTO对象
     */
    private BizPatientInfoDTO convertToDTO(BizPatientInfo entity) {
        BizPatientInfoDTO dto = new BizPatientInfoDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 设置性别描述
        setGenderDesc(dto, entity.getGender());
        
        return dto;
    }

    /**
     * 转换实体为列表DTO（不包含大字段）
     * 
     * @param entity 实体对象
     * @return ListDTO对象
     */
    private BizPatientInfoListDTO convertToListDTO(BizPatientInfo entity) {
        BizPatientInfoListDTO dto = new BizPatientInfoListDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 设置性别描述
        if (entity.getGender() != null) {
            switch (entity.getGender()) {
                case 0:
                    dto.setGenderDesc("未知");
                    break;
                case 1:
                    dto.setGenderDesc("男");
                    break;
                case 2:
                    dto.setGenderDesc("女");
                    break;
                default:
                    dto.setGenderDesc("未知");
            }
        }
        
        return dto;
    }

    /**
     * 设置性别描述
     * 
     * @param dto 需要设置性别描述的DTO对象
     * @param gender 性别值
     */
    private void setGenderDesc(BizPatientInfoDTO dto, Integer gender) {
        if (gender != null) {
            switch (gender) {
                case 0:
                    dto.setGenderDesc("未知");
                    break;
                case 1:
                    dto.setGenderDesc("男");
                    break;
                case 2:
                    dto.setGenderDesc("女");
                    break;
                default:
                    dto.setGenderDesc("未知");
            }
        }
    }
}