package com.yunzheng.rd.scaffold;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
// 扫描路径
@ComponentScan(basePackages = {
        "com.yunzheng.rd.scaffold", "com.yunzheng.capability.rd"
})
// SDK中的repository包路径
@EnableJpaRepositories(basePackages = {
        "com.yunzheng.rd.scaffold", "com.yunzheng.capability.rd"
})
// SDK中的实体包路径
@EntityScan(basePackages = {
        "com.yunzheng.rd.scaffold", "com.yunzheng.capability.rd"
})
public class WebApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }
}
