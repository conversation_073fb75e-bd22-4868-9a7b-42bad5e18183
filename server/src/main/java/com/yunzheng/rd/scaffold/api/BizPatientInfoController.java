package com.yunzheng.rd.scaffold.api;

import com.yunzheng.capability.commons.model.vo.PageVO;
import com.yunzheng.capability.commons.model.vo.ResponseVO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoDeleteDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoListDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoQueryDTO;
import com.yunzheng.rd.scaffold.dto.BizPatientInfoUpdateDTO;
import com.yunzheng.rd.scaffold.service.BizPatientInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 患者信息控制器
 * 提供患者信息管理的REST API接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@RestController
@RequestMapping("/api/patient-info")
public class BizPatientInfoController {


    @Autowired
    private BizPatientInfoService patientInfoService;

    /**
     * 根据ID查询患者信息详情
     * 返回包含病例原始文本在内的完整患者信息
     * 
     * @param id 患者ID
     * @return 患者详细信息
     */
    @GetMapping("/detail")
    public ResponseVO<BizPatientInfoDTO> getById(@RequestParam Long id) {
        log.info("查询患者信息，ID: {}", id);
        return patientInfoService.getById(id);
    }

    /**
     * 分页查询患者信息列表
     * 支持根据病历号和患者姓名进行模糊查询
     * 注意：分页查询不返回病例原始文本等大字段，提高查询性能
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    public ResponseVO<PageVO<BizPatientInfoListDTO>> page(@RequestBody BizPatientInfoQueryDTO queryDTO) {
        log.info("分页查询患者信息，查询条件: {}", queryDTO);
        return patientInfoService.page(queryDTO);
    }


    /**
     * 更新患者信息
     * 
     * @param updateDTO 更新参数
     * @return 更新结果
     */
    @PostMapping("/update")
    public ResponseVO<BizPatientInfoDTO> update(@RequestBody @Validated BizPatientInfoUpdateDTO updateDTO) {
        log.info("更新患者信息，ID: {}", updateDTO.getId());
        return patientInfoService.update(updateDTO);
    }


    /**
     * 删除患者信息
     * 
     * @param deleteDTO 删除参数
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ResponseVO<Boolean> delete(@RequestBody @Validated BizPatientInfoDeleteDTO deleteDTO) {
        log.info("删除患者信息，ID: {}", deleteDTO.getId());
        return patientInfoService.delete(deleteDTO);
    }

}