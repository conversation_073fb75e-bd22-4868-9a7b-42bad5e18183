package com.yunzheng.rd.scaffold.domain;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者信息实体类
 * 对应数据表: biz_patient_info
 * 存储患者的基本信息和医疗相关数据
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Entity
@Table(name = "biz_patient_info")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizPatientInfo {

    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 病历号，唯一标识
     */
    @Column(name = "medical_record_number", nullable = false, unique = true, length = 50)
    private String medicalRecordNumber;

    /**
     * 病人姓名
     */
    @Column(name = "patient_name", nullable = false, length = 100)
    private String patientName;

    /**
     * 性别：0未知 1男 2女
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * 病种
     */
    @Column(name = "disease_type", length = 100)
    private String diseaseType;

    /**
     * 入院时间
     */
    @Column(name = "admission_time")
    private LocalDateTime admissionTime;

    /**
     * 得分（支持两位小数）
     */
    @Column(name = "score", precision = 5, scale = 2)
    private BigDecimal score;

    /**
     * 既往史
     */
    @Column(name = "past_history", columnDefinition = "TEXT")
    private String pastHistory;

    /**
     * 手术史
     */
    @Column(name = "surgical_history", columnDefinition = "TEXT")
    private String surgicalHistory;

    /**
     * 既往用药史
     */
    @Column(name = "past_medication_history", columnDefinition = "TEXT")
    private String pastMedicationHistory;

    /**
     * 居住地
     */
    @Column(name = "residence", length = 200)
    private String residence;

    /**
     * 联系方式（可包含多种方式）
     */
    @Column(name = "contact_info", length = 200)
    private String contactInfo;

    /**
     * 电话
     */
    @Column(name = "phone", length = 20)
    private String phone;

    /**
     * 出生日期
     */
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    /**
     * 主诊断疾病
     */
    @Column(name = "main_diagnosis", length = 200)
    private String mainDiagnosis;

    /**
     * 紧急联系人姓名
     */
    @Column(name = "emergency_contact_name", length = 100)
    private String emergencyContactName;

    /**
     * 紧急联系人手机号
     */
    @Column(name = "emergency_contact_phone", length = 20)
    private String emergencyContactPhone;

    /**
     * 病例原始文本
     */
    @Column(name = "case_raw_text", columnDefinition = "TEXT")
    private String caseRawText;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}