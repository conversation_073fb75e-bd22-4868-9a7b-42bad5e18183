package com.yunzheng.rd.scaffold.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者信息列表DTO
 * 用于分页查询列表的患者信息数据传输对象
 * 不包含病例原始文本等大字段信息
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizPatientInfoListDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 病历号
     */
    private String medicalRecordNumber;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 性别：0未知 1男 2女
     */
    private Integer gender;

    /**
     * 性别描述
     */
    private String genderDesc;

    /**
     * 病种
     */
    private String diseaseType;

    /**
     * 入院时间
     */
    private LocalDateTime admissionTime;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 居住地
     */
    private String residence;

    /**
     * 电话
     */
    private String phone;

    /**
     * 出生日期
     */
    private LocalDate dateOfBirth;

    /**
     * 主诊断疾病
     */
    private String mainDiagnosis;

    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 紧急联系人手机号
     */
    private String emergencyContactPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
