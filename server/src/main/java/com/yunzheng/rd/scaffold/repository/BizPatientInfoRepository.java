package com.yunzheng.rd.scaffold.repository;

import com.yunzheng.rd.scaffold.domain.BizPatientInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 患者信息Repository接口
 * 提供患者信息的数据访问功能
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Repository
public interface BizPatientInfoRepository extends JpaRepository<BizPatientInfo, Long>, JpaSpecificationExecutor<BizPatientInfo> {

    /**
     * 根据病历号查找患者
     * 
     * @param medicalRecordNumber 病历号
     * @return 患者信息
     */
    Optional<BizPatientInfo> findByMedicalRecordNumber(String medicalRecordNumber);

    /**
     * 检查病历号是否存在
     * 
     * @param medicalRecordNumber 病历号
     * @return 是否存在
     */
    boolean existsByMedicalRecordNumber(String medicalRecordNumber);

    /**
     * 检查病历号是否存在（排除指定ID）
     * 
     * @param medicalRecordNumber 病历号
     * @param id 要排除的ID
     * @return 是否存在
     */
    boolean existsByMedicalRecordNumberAndIdNot(String medicalRecordNumber, Long id);

    /**
     * 根据病历号和患者姓名进行模糊查询
     * 
     * @param medicalRecordNumber 病历号（可为空）
     * @param patientName 患者姓名（可为空）
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Query("SELECT p FROM BizPatientInfo p WHERE " +
           "(:medicalRecordNumber IS NULL OR p.medicalRecordNumber LIKE %:medicalRecordNumber%) AND " +
           "(:patientName IS NULL OR p.patientName LIKE %:patientName%) " +
           "ORDER BY p.createdAt DESC")
    Page<BizPatientInfo> findByMedicalRecordNumberAndPatientName(
            @Param("medicalRecordNumber") String medicalRecordNumber,
            @Param("patientName") String patientName,
            Pageable pageable);
}