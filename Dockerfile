# Build stage
FROM eclipse-temurin:21-jdk-alpine AS builder

# Use China mirror for faster package installation
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update && \
    apk add --no-cache maven

# Set working directory
WORKDIR /app

# Copy Maven settings for private repository
RUN mkdir -p /root/.m2
COPY build/settings.xml /root/.m2/settings.xml

# Copy POM files first for better layer caching
COPY pom.xml .
COPY server/pom.xml server/

# Download dependencies with parallel downloads and optimized settings
ARG USE_CACHE=true
RUN if [ "$USE_CACHE" = "true" ]; then \
      mvn dependency:go-offline -B -T 4 --settings /root/.m2/settings.xml \
        -Dmaven.artifact.threads=10 \
        -Dmaven.resolver.transport=wagon; \
    else \
      mvn dependency:go-offline -B -T 4 --settings /root/.m2/settings.xml -U \
        -Dmaven.artifact.threads=10 \
        -Dmaven.resolver.transport=wagon; \
    fi

# Force update SNAPSHOT dependencies with parallel processing
RUN mvn dependency:resolve -DincludeScope=runtime -B -T 4 --settings /root/.m2/settings.xml -U \
    -Dmaven.artifact.threads=10 || true

# Copy source code
COPY . .

# Build the application with parallel compilation
RUN if [ "$USE_CACHE" = "true" ]; then \
      mvn clean package -DskipTests -B -T 4 --settings /root/.m2/settings.xml \
        -Dmaven.compile.fork=true \
        -Dmaven.compiler.maxmem=1024m; \
    else \
      mvn clean package -DskipTests -B -T 4 --settings /root/.m2/settings.xml -U \
        -Dmaven.compile.fork=true \
        -Dmaven.compiler.maxmem=1024m; \
    fi

# Runtime stage
FROM eclipse-temurin:21-jdk-alpine

# Create user and group
RUN addgroup -g 9292 yzuser && \
    adduser -u 9292 -G yzuser -D yzuser

# Install necessary tools
RUN apk update && \
    apk add --no-cache procps curl bash

# Copy application jar file from build stage
COPY --from=builder /app/server/target/server.jar /home/<USER>

# Start command
CMD java -jar /home/<USER>