# RD-Scaffold 项目

## 项目介绍

RD-Scaffold 是一个基于 Spring Boot 的应用程序脚手架，提供了一套标准化的项目结构和配置。该项目旨在帮助开发人员快速搭建新的应用，专注于业务逻辑的开发，而不必花费过多时间在项目结构和基础设施的搭建上。

## 技术栈

- Java 21
- Spring Boot 3.4.1
- Spring Data JPA
- PostgreSQL 数据库
- Maven

## 项目结构

```
rd-scaffold/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── yunzheng/
│   │   │           └── rd/
│   │   │               └── scaffold/
│   │   │                   ├── api/                   # API 接口层
│   │   │                   ├── application/           # 应用服务层
│   │   │                   ├── configuration/         # 配置类
│   │   │                   ├── domain/                # 领域模型层
│   │   │                   ├── exception/             # 异常处理
│   │   │                   ├── infrastructure/        # 基础设施层
│   │   │                   ├── repository/            # 数据访问层
│   │   │                   ├── util/                  # 工具类
│   │   │                   └── WebApplication.java    # 应用启动类
│   │   └── resources/
│   │       ├── application.yml             # 通用配置
│   │       ├── application-dev.yml         # 开发环境配置
│   │       ├── application-prod.yml        # 生产环境配置
│   │       ├── application-generated.yml   # 生成的配置
│   │       └── logback-prod.xml            # 生产环境日志配置
│   └── test/                               # 测试代码
├── pom.xml                                 # Maven 配置文件
└── README.md                               # 项目说明文档
```

## 环境依赖

- JDK 21+
- Maven 3.8+
- PostgreSQL 数据库

## 快速开始

### 配置数据库

1. 确保 PostgreSQL 数据库已安装并运行
2. 默认配置使用以下数据库连接信息：
   - URL: jdbc:postgresql://ip:port/数据库名
   - 用户名: 数据库用户名
   - 密码: 数据库密码

### 编译和运行

```bash
# 编译项目
mvn clean package

```

### 访问应用

应用启动后，可通过以下地址访问：
- 开发环境: http://localhost:${serverver.port}/${server.servlet.context-path}/

## 项目特性

- 采用 DDD (领域驱动设计) 分层架构
- 集成了常用的开发框架和工具
- 支持开发、生产多环境配置
- 提供了标准化的日志配置
