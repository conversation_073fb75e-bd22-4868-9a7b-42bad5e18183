<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.1</version>
        <relativePath/>
    </parent>
    
    <groupId>com.yunzheng</groupId>
    <artifactId>rd-scaffold</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>server</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--通用能力依赖-->
            <dependency>
                <groupId>com.yunzheng.capability</groupId>
                <artifactId>login</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>


            <dependency>
                <groupId>com.yunzheng.capability</groupId>
                <artifactId>sysconfig</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.yunzheng.capability</groupId>-->
<!--                <artifactId>audit-log</artifactId>-->
<!--                <version>1.0.0-SNAPSHOT</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.yunzheng.capability</groupId>
                <artifactId>registration</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!--内置依赖-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.2.23</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.21</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.36</version>
            </dependency>

            <dependency>
                <groupId>com.yunzheng.capability</groupId>
                <artifactId>graphql</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yunzheng.capability</groupId>
                <artifactId>openapi</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.yunzheng.rd.scaffold.WebApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>