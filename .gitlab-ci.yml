stages:
  - build

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1

docker-build-push:
  stage: build
  image: docker:latest
  tags:
    - cloud-dev
  services:
    - name: docker:dind
      alias: docker
      command: ["--insecure-registry=$HARBOR_REGISTRY"]
  before_script:
    - mkdir -p ~/.docker
    - |
      cat > ~/.docker/config.json << EOF
      {
        "experimental": "enabled",
        "insecure-registries": ["$HARBOR_REGISTRY"]
      }
      EOF
    # Login to Harbor registry
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin
  script:
    # Check .no-cache file content to determine build strategy
    - |
      if [ -f ".no-cache" ]; then
        CACHE_CONTENT=$(cat .no-cache | grep -v '^#' | grep -v '^$' | head -n 1 | tr -d '[:space:]')
        echo "📁 Found .no-cache file with content: '$CACHE_CONTENT'"
      
        if [ "$CACHE_CONTENT" = "no-cache" ] || [ "$CACHE_CONTENT" = "force" ] || [ "$CACHE_CONTENT" = "rebuild" ]; then
          echo "🚫 No-cache build triggered by file content!"
          echo "📝 .no-cache file content:"
          cat .no-cache
          echo "🔨 Building with --no-cache and forcing Maven dependency refresh..."
          docker build --no-cache --build-arg USE_CACHE=false --add-host maven.yunzheng.work:************ -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .
        else
          echo "⚡ Cache-enabled build (.no-cache file exists but content doesn't trigger no-cache)"
          echo "💡 Valid trigger words: 'no-cache', 'force', 'rebuild'"
          docker build --add-host maven.yunzheng.work:************ -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .
        fi
      elif [ "$FORCE_NO_CACHE" = "true" ]; then
        echo "🚫 No-cache build triggered by FORCE_NO_CACHE variable!"
        echo "🔨 Building with --no-cache and forcing Maven dependency refresh..."
        docker build --no-cache --build-arg USE_CACHE=false --add-host maven.yunzheng.work:************ -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .
      else
        echo "⚡ Cache-enabled build (normal mode)"
        echo "💡 To force dependency update:"
        echo "   1. Create .no-cache file with content: 'no-cache', 'force', or 'rebuild'"
        echo "   2. Or set FORCE_NO_CACHE=true variable"
        docker build --add-host maven.yunzheng.work:************ -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .
      fi
    
    # Tag the same image as latest
    - docker tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    
    # Push both tags to insecure registry
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    
    # Output success message
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA"
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest"
  only:
    - dev
    - main